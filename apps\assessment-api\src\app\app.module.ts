import { Auth0Module, Auth0Service } from '@microservices/auth';
import { CodeExecutorModule } from '@microservices/code-executor';
import { DbModule } from '@microservices/db';
import { EditorManagerModule } from '@microservices/editor-manager';
import { EmailModule } from '@microservices/email';
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ArchivateService } from 'tools/archivate.service';
import { ArchivateSqliteService } from 'tools/archiveWithSqlite.service';
import { SqliteGenerator } from 'tools/sqlite.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AssessmentDatabaseController } from './assessment-database/assessment-database.controller';
import { AssessmentChatbotController } from './assessment-chatbot/assessment-chatbot.controller';
import { AssignmentController } from './assignment/assignment.controller';
import { CalendarController } from './calendar/calendar.controller';
import { CodingAreaGateway } from './coding-area/coding-area.gateway';
import { DomainQuestionsController } from './domain-questions/domain-questions.controller';
import { LiveCodingController } from './live-coding/live-coding.controller';
import { TakeHomeTaskController } from './take-home/take-home-task.controller';
import { TakeHomeSubmissionController } from './take-home/take-home-submission.controller';
import { TestCaseController } from './testcases/testcase.controller';
import { TestManagerController } from './test-manager/test-manager.controller';
import { DomainResultController } from './domain-result/domain-result.controller';
import { HttpModule } from '@nestjs/axios';
import { WorkerProvider } from 'apps/temporal/src/app/workflow/worker.provider';
import { ActivitiesModule } from 'apps/temporal/src/app/workflow/activities/activities.module';
import { ActivitiesService } from 'apps/temporal/src/app/workflow/activities/activities.service';
import { TemporalClientModule } from 'apps/temporal/src/app/workflow/temporal-client/temporalClient.module';
import { TemporalClientService } from 'apps/temporal/src/app/workflow/temporal-client/temporalClient.services';
import { TestCaseModule } from './testcases/testcase.module';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'ASSESSMENT_SERVICE',
        transport: Transport.REDIS,
        options: {
          url: process.env.REDIS_URL || 'redis://localhost:6379',
        },
      },
    ]),
    DbModule,
    Auth0Module,
    EmailModule,
    EditorManagerModule,
    CodeExecutorModule,
    ArchivateService,
    SqliteGenerator,
    ArchivateSqliteService,
    HttpModule,
    ActivitiesModule,
    TemporalClientModule,
    TestCaseModule
  ],
  controllers: [
    AppController,
    DomainQuestionsController,
    AssignmentController,
    AssessmentDatabaseController,
    AssessmentChatbotController,
    LiveCodingController,
    TakeHomeTaskController,
    TakeHomeSubmissionController,
    CalendarController,
    TestManagerController,
    DomainResultController,
    TestCaseController
  ],
  providers: [...WorkerProvider, AppService, CodingAreaGateway, ArchivateService, ArchivateSqliteService, SqliteGenerator, Auth0Service, ActivitiesService, TemporalClientService],
})
export class AppModule { }
