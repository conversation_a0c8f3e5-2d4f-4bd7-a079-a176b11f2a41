import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { AssessmentDatabase } from './assessment-database/assessmentDatabase.model';
import { AssessmentDatabaseModule } from './assessment-database/assessmentDatabase.module';
import { AssessmentDatabaseService } from './assessment-database/assessmentDatabase.service';
import { AssessmentChatbot } from './assessment-chatbot/assessmentChatbot.model';
import { AssessmentChatbotModule } from './assessment-chatbot/assessmentChatbot.module';
import { AssessmentChatbotService } from './assessment-chatbot/assessmentChatbot.service';
import { Assignment } from './assignment/assignment.model';
import { AssignmentModule } from './assignment/assignment.module';
import { AssignmentService } from './assignment/assignment.service';
import { CalendarModule } from './calendar/calendar.module';
import { CalendarService } from './calendar/calendar.service';
import { EventUser } from './calendar/event-user.model';
import { Event } from './calendar/event.model';
import { CodingArea } from './coding-area/coding-area.model';
import { CodingAreaModule } from './coding-area/coding-area.module';
import { CodingAreaService } from './coding-area/coding-area.service';
import { DomainAssessment } from './domain-questions/domain-assessment.model';
import { DomainQuestions } from './domain-questions/domain-questions.model';
import { DomainQuestionsModule } from './domain-questions/domain-questions.module';
import { DomainQuestionsService } from './domain-questions/domain-questions.service';
import { GoogleAuth } from './googleAuth/googleAuth.model';
import { GoogleAuthModule } from './googleAuth/googleAuth.module';
import { GoogleAuthModelService } from './googleAuth/googleAuth.service';
import { LiveCoding } from './live-coding/live-coding.model';
import { LiveCodingModule } from './live-coding/live-coding.module';
import { LiveCodingService } from './live-coding/live-coding.service';
import { LiveCodingDrafts } from './live-coding/live-drafts/live-drafts.model';
import { LiveDraftsModule } from './live-coding/live-drafts/live-drafts.module';
import { LiveDraftsService } from './live-coding/live-drafts/live-drafts.service';
import { Package } from './packages/packages.model';
import { Questions } from './questions/questions.model';
import { QuestionsModule } from './questions/questions.module';
import { QuestionsService } from './questions/questions.service';
import { ReviewAndScore } from './reviewAndScore/reviewAndScore.model';
import { ReviewAndScoreModule } from './reviewAndScore/reviewAndScore.module';
import { ReviewAndScoreService } from './reviewAndScore/reviewAndScore.service';
import { TakeHomeDrafts } from './take-home/home-drafts/home-drafts.model';
import { TakeHomeDraftsModule } from './take-home/home-drafts/home-drafts.module';
import { TakeHomeDraftsService } from './take-home/home-drafts/home-drafts.service';
import { TakeHomeTask } from './take-home/take-home-task.model';
import { TakeHomeTaskModule } from './take-home/take-home-task.module';
import { TakeHomeTaskService } from './take-home/take-home-task.service';
import { TakeHomeSubmission } from './take-home/take-home-submission.model';
import { TakeHomeSubmissionModule } from './take-home/take-home-submission.module';
import { TakeHomeSubmissionService } from './take-home/take-home-submission.service';
import { TestCase } from './testcases/testcase.model';
import { TestCaseModule } from './testcases/testcase.module';
import { TestCaseService } from './testcases/testcase.service';
import { Deadline } from './deadline/deadline.model';
import { TimeDuration } from './time-duration/time-duration.model';
import { TestManager } from './test-manager/test-manager.model';
import { TestManagerModule } from './test-manager/test-manager.module';
import { TestManagerService } from './test-manager/test-manager.service';
import { DomainResults } from './domain-result/domain-result.model';
import { DomainAnswers } from './domain-result/domain-answer.model';
import { DomainResultModule } from './domain-result/domain-result.module';
import { DomainResultService } from './domain-result/domain-result.service';
import { Languages } from './languages/languages.model';
import { LanguagesModule } from './languages/languages.module';
import { LanguagesService } from './languages/languages.service';
import { CodeExecution } from './code-execution/code-execution.model';
import { CodeExecutionModule } from './code-execution/code-execution.module';
import { CodeExecutionService } from './code-execution/code-execution.service';
import { EmailModule } from "@microservices/email";

@Module({
  imports: [
    ConfigModule.forRoot(),
    SequelizeModule.forRoot({
      dialect: 'postgres',
      host: process.env.DB_HOST,
      port: +process.env.DB_PORT,
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      autoLoadModels: true,
      logging: false,
      models: [
        CodingArea,
        DomainQuestions,
        DomainAssessment,
        TestCase,
        Assignment,
        AssessmentDatabase,
        AssessmentChatbot,
        LiveCoding,
        TakeHomeTask,
        TakeHomeSubmission,
        Questions,
        LiveCodingDrafts,
        TakeHomeDrafts,
        GoogleAuth,
        ReviewAndScore,
        Event,
        EventUser,
        Package,
        Deadline,
        TimeDuration,
        TestManager,
        DomainResults,
        DomainAnswers,
        TimeDuration,
        Deadline,
        Languages,
        CodeExecution
      ],
    }),
    SequelizeModule.forRoot({
      name: 'assessmentMYSQL',
      dialect: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
    }),
    SequelizeModule.forRoot({
      name: 'assessmentPG',
      dialect: 'postgres',
      host: 'dummy-database.crbdnvdwwcn1.us-east-1.rds.amazonaws.com',
      port: 5432,
      username: 'postgres',
      password: 'Test1359',
      database: 'postgres',
    }),
    CodingAreaModule,
    DomainQuestionsModule,
    TestCaseModule,
    AssignmentModule,
    AssessmentDatabaseModule,
    AssessmentChatbotModule,
    LiveCodingModule,
    TakeHomeTaskModule,
    TakeHomeSubmissionModule,
    QuestionsModule,
    LiveDraftsModule,
    TakeHomeDraftsModule,
    GoogleAuthModule,
    ReviewAndScoreModule,
    CalendarModule,
    TestManagerModule,
    DomainResultModule,
    LanguagesModule,
    CodeExecutionModule,
    EmailModule
  ],
  providers: [
    CodingAreaService,
    DomainQuestionsService,
    TestCaseService,
    AssignmentService,
    AssessmentDatabaseService,
    AssessmentChatbotService,
    LiveCodingService,
    TakeHomeTaskService,
    TakeHomeSubmissionService,
    QuestionsService,
    LiveDraftsService,
    TakeHomeDraftsService,
    GoogleAuthModelService,
    ReviewAndScoreService,
    CalendarService,
    TestManagerService,
    DomainResultService,
    LanguagesService,
    CodeExecutionService
  ],
  exports: [
    CodingAreaService,
    DomainQuestionsService,
    TestCaseService,
    AssignmentService,
    AssessmentDatabaseService,
    AssessmentChatbotService,
    AssessmentChatbotModule,    
    LiveCodingService,
    TakeHomeTaskService,
    TakeHomeSubmissionService,
    QuestionsService,
    LiveDraftsService,
    TakeHomeDraftsService,
    GoogleAuthModelService,
    ReviewAndScoreService,
    CalendarService,
    TestManagerService,
    DomainResultService,
    LanguagesService,
    CodeExecutionService,
    SequelizeModule,
  ],
})
export class DbModule { }
