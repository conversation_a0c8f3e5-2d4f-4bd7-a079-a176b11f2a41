import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { AssessmentCategory } from '../domain-questions/domain-assessment.model';
import { ReviewAndScore } from './reviewAndScore.model';
import { ReviewFilter } from './reviewFilter.interface';

@Injectable()
export class ReviewAndScoreService {
  constructor(
    @InjectModel(ReviewAndScore)
    private reviewService: typeof ReviewAndScore
  ) {}

  findOneById(id: number): Promise<ReviewAndScore> {
    return this.reviewService.findByPk(id);
  }

  findAllByAssessment(id: number, type: AssessmentCategory): Promise<ReviewAndScore[]> {
    return this.reviewService.findAll({
      where: {
        id,
        assessmentType: type,
      },
    });
  }

  findAll(): Promise<ReviewAndScore[]> {
    return this.reviewService.findAll();
  }

  create(dto: ReviewAndScore): Promise<ReviewAndScore> {
    return this.reviewService.create(dto);
  }

  update(id: number, dto: ReviewAndScore) {
    return this.reviewService.update({ ...dto }, { where: { id } });
  }

  async remove(id: number) {
    return this.reviewService.destroy({ where: { id } });
  }

  findByCandidate(candidateId: number, assessmentId: number, assessmentType: AssessmentCategory): Promise<ReviewAndScore> {
    return this.reviewService.findOne({
      where: {
        userId: candidateId,
        assessmentId,
        assessmentType,
      },
    });
  }

  findAllByCandidateAndType(candidateId: number, assessmentType: AssessmentCategory): Promise<ReviewAndScore[]> {
    return this.reviewService.findAll({
      where: {
        userId: candidateId,
        assessmentType,
      },
    });
  }

  async filterDomainAssessment(data: ReviewFilter) {
    const parsedQuery = {
      name: data.assessmentName ? data.assessmentName.split(',') : null,
      title: data.title ? data.title.split(',') : null,
      industry: data.industry ? data.industry.split(',') : null,
      department: data.department ? data.department.split(',') : null,
    };

    Object.keys(parsedQuery).forEach((key) => {
      if (parsedQuery[key] === null) {
        delete parsedQuery[key];
      }
    });

    const propertyArray = [];

    for (const key in parsedQuery) {
      if (key === 'name') {
        propertyArray.push({ assessmentName: parsedQuery[key] });
      } else if (key === 'title') {
        propertyArray.push({ jobTitle: parsedQuery[key] });
      } else if (key === 'industry') {
        propertyArray.push({ industry: parsedQuery[key] });
      } else if (key === 'department') {
        propertyArray.push({ department: parsedQuery[key] });
      }
    }

    return this.reviewService.findAll({
      include: [{ association: 'domainAssessment', where: { [Op.or]: propertyArray } }],
    });
  }
}
